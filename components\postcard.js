import React from "react"
import Link from "next/link"
import Image from "next/image"
import Moment from "react-moment"
import { getStrapiMedia } from "../lib/media"

const Postcard = ({ post }) => {
  return (
    <div className="col-4">
        <Link href={`/blog/${post.attributes.slug}`} className="">
            <div className="blog-group-item">
                    <div className="img-holder" style={{position: "relative", width: "100%", height: "250px", overflow: "hidden"}}>
                        {post.attributes.image?.data ? (
                            <Image
                                src={getStrapiMedia(post.attributes.image)}
                                alt={`blog group image ${post.attributes.title}`}
                                fill
                                className="img-fluid"
                                style={{objectFit: "cover"}}
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            />
                        ) : (
                            <Image
                                src="/images/main-img.jpg"
                                alt="Default blog image"
                                fill
                                className="img-fluid"
                                style={{objectFit: "cover"}}
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            />
                        )}
                    </div>
                    <div className="txt-holder">
                        <h3>{post.attributes.title}</h3>
                        <h5><Moment format="MM/DD/YYYY">{post.attributes.date}</Moment></h5>
                        <p>{post.attributes.description}</p>
                        <span className="btn link-btn underline-btn">Read Article →</span>
                    </div>
                </div>
        </Link>
    </div>
  )
}

export default Postcard
