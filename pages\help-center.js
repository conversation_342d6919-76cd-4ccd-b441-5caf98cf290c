import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import Faq from "../components/faq"
import { fetchAPI } from "../lib/api"
import Image from 'next/image'
import Link from 'next/link'
import { getStrapiMedia } from "../lib/media"

const Help = ({ pages, faqs }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(pages.attributes.headerimage)} width="1920" height="300" alt="Ethos" className="img-fluid" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="help-center-main-section">
                <div className="container mw-900">
                    <div className="row mb-80">
                    {(pages.attributes.sections).map((sect, i) => {
                            if(i == 0){
                                return (
                                    <div className="col-12 txt-holder text-center">
                                        <h5>{sect.smalltitle}</h5>
                                        <h3>{sect.title}</h3>
                                        <p>{sect.description}</p>
                                    </div>
                                )
                            }
                        })}
                    </div>
                    <div className="row">
                        <div className="col-12">
                            <div id="accordion">
                                {faqs.map((faq, i) => {
                                    return (
                                        <Faq faq={faq} key={`help_faq_key_${i}`} />
                                    )
                                })}
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="help-center-cta-section">
                <div className="container help-center-cta-container">
                        {(pages.attributes.sections).map((sect, i) => {
                            if(i == 1){
                                return (
                                    <div className="row help-center-cta-content">
                                        <div className="col-6 txt-holder">
                                            <h5>{sect.smalltitle}</h5>
                                            <h3>{sect.title}</h3>
                                            <p>{sect.description}</p>
                                            <Link href="/contact-us" className="btn second-bg white box-shadow">CONTACT US</Link>
                                        </div>
                                        <div className="col-6 img-holder order-md-1">
                                            <Image src={getStrapiMedia(sect.image)} width={sect.image.data.attributes.width} height={sect.image.data.attributes.height} alt="Ethos" className="img-fluid" />
                                        </div>
                                    </div>
                                )
                            }
                        })}
                </div>
            </section>
        </main>
    </Layout>
  )
}


export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/5", {
            populate: {
                sections: {
                    populate: {
                        image: "*"
                    }
                },
                headerimage: {
                    populate: "*"
                },
                seo: {
                    populate: "*"
                }
            }
        })
        const faqsRes = await fetchAPI("/faqs", {populate: "*"})
        return {
            props: {
                pages: pagesRes.data || null,
                faqs: faqsRes.data || [],
            }
        }
    } catch (error) {
        console.error("Error fetching help center data:", error);
        return {
            props: {
                pages: null,
                faqs: [],
            }
        }
    }
}

export default Help