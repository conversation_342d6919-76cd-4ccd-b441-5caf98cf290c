
> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production

> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production

> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production

> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
FetchError: request to http://localhost:1337/uploads/index_header_13e667cd20.jpg failed, reason: connect ECONNREFUSED 127.0.0.1:1337
    at ClientRequest.<anonymous> (/home/<USER>/snmmdrmxhc/public_html/node_modules/node-fetch/lib/index.js:1461:11)
    at ClientRequest.emit (events.js:400:28)
    at Socket.socketErrorListener (_http_client.js:475:9)
    at Socket.emit (events.js:400:28)
    at emitErrorNT (internal/streams/destroy.js:106:8)
    at emitErrorCloseNT (internal/streams/destroy.js:74:3)
    at processTicksAndRejections (internal/process/task_queues.js:82:21) {
  type: 'system',
  errno: 'ECONNREFUSED',
  code: 'ECONNREFUSED'
}
FetchError: request to http://localhost:1337/uploads/index_video_bg_a1a450f330.jpg failed, reason: connect ECONNREFUSED 127.0.0.1:1337
    at ClientRequest.<anonymous> (/home/<USER>/snmmdrmxhc/public_html/node_modules/node-fetch/lib/index.js:1461:11)
    at ClientRequest.emit (events.js:400:28)
    at Socket.socketErrorListener (_http_client.js:475:9)
    at Socket.emit (events.js:400:28)
    at emitErrorNT (internal/streams/destroy.js:106:8)
    at emitErrorCloseNT (internal/streams/destroy.js:74:3)
    at processTicksAndRejections (internal/process/task_queues.js:82:21) {
  type: 'system',
  errno: 'ECONNREFUSED',
  code: 'ECONNREFUSED'
}
FetchError: request to http://localhost:1337/uploads/index_tracking_d16b241a67.jpg failed, reason: connect ECONNREFUSED 127.0.0.1:1337
    at ClientRequest.<anonymous> (/home/<USER>/snmmdrmxhc/public_html/node_modules/node-fetch/lib/index.js:1461:11)
    at ClientRequest.emit (events.js:400:28)
    at Socket.socketErrorListener (_http_client.js:475:9)
    at Socket.emit (events.js:400:28)
    at emitErrorNT (internal/streams/destroy.js:106:8)
    at emitErrorCloseNT (internal/streams/destroy.js:74:3)
    at processTicksAndRejections (internal/process/task_queues.js:82:21) {
  type: 'system',
  errno: 'ECONNREFUSED',
  code: 'ECONNREFUSED'
}
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production
FetchError: request to http://localhost:1337/api/global?populate%5Bfavicon%5D=%2A&populate%5BdefaultSeo%5D%5Bpopulate%5D=%2A&populate%5Bsocial%5D=%2A failed, reason: connect ECONNREFUSED 127.0.0.1:1337
    at ClientRequest.<anonymous> (/home/<USER>/snmmdrmxhc/public_html/node_modules/node-fetch/lib/index.js:1461:11)
    at ClientRequest.emit (events.js:400:28)
    at Socket.socketErrorListener (_http_client.js:475:9)
    at Socket.emit (events.js:400:28)
    at emitErrorNT (internal/streams/destroy.js:106:8)
    at emitErrorCloseNT (internal/streams/destroy.js:74:3)
    at processTicksAndRejections (internal/process/task_queues.js:82:21) {
  type: 'system',
  errno: 'ECONNREFUSED',
  code: 'ECONNREFUSED'
}

> my-next-blog@0.1.5 develop /home/<USER>/snmmdrmxhc/public_html
> next dev

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
info  - Using webpack 5. Reason: Enabled by default https://nextjs.org/docs/messages/webpack5
warn  - ./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[2]!./assets/css/style.css
Warning

(3233:11) postcss-preset-env: end value has mixed support, consider using flex-end instead
info  - ready on http://localhost:3000
<w> [webpack.cache.PackFileCacheStrategy] Skipped not serializable cache item 'Compilation/modules|/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[1]!/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[2]!/home/<USER>/snmmdrmxhc/public_html/assets/css/style.css': No serializer registered for Warning
<w> while serializing webpack/lib/cache/PackFileCacheStrategy.PackContentItems -> webpack/lib/NormalModule -> Array { 1 items } -> webpack/lib/ModuleWarning -> Warning
event - build page: /pricing
wait  - compiling...
warn  - ./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[2]!./assets/css/style.css
Warning

(3233:11) postcss-preset-env: end value has mixed support, consider using flex-end instead
info  - ready on http://localhost:3000
Not Found
event - build page: /_error
wait  - compiling...
warn  - ./node_modules/next/dist/compiled/css-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[1]!./node_modules/next/dist/compiled/postcss-loader/cjs.js??ruleSet[1].rules[3].oneOf[6].use[2]!./assets/css/style.css
Warning

(3233:11) postcss-preset-env: end value has mixed support, consider using flex-end instead
info  - ready on http://localhost:3000
error - lib/api.js (42:10) @ fetchAPI
Error: An error occured please try again
[0m [90m 40 | [39m[32m  if (!response.ok) {[39m[0m
[0m [90m 41 | [39m[32m    console.error(response.statusText)[39m[0m
[0m[31m[1m>[22m[39m[90m 42 | [39m[32m    throw new Error(`[39m[33mAn[39m error occured please [36mtry[39m again[32m`)[39m[0m
[0m [90m    | [39m         [31m[1m^[22m[39m[0m
[0m [90m 43 | [39m[32m  }[39m[0m
[0m [90m 44 | [39m[32m  const data = await response.json()[39m[0m
[0m [90m 45 | [39m[32m  return data[39m[0m

> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Error: Could not find a production build in the '/home/<USER>/snmmdrmxhc/public_html/.next' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id
    at Server.readBuildId (/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next-server.js:1578:23)
    at new Server (/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next-server.js:92:29)
    at NextServer.createServer (/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next.js:107:16)
    at async /home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next.js:119:31
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! my-next-blog@0.1.5 start: `next start`
npm ERR! Exit status 1
npm ERR! 
npm ERR! Failed at the my-next-blog@0.1.5 start script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.

npm ERR! A complete log of this run can be found in:
npm ERR!     /home/<USER>/.npm/_logs/2022-01-05T20_02_46_788Z-debug.log

> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Error: Could not find a production build in the '/home/<USER>/snmmdrmxhc/public_html/.next' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id
    at Server.readBuildId (/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next-server.js:1578:23)
    at new Server (/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next-server.js:92:29)
    at NextServer.createServer (/home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next.js:107:16)
    at async /home/<USER>/snmmdrmxhc/public_html/node_modules/next/dist/server/next.js:119:31
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! my-next-blog@0.1.5 start: `next start`
npm ERR! Exit status 1
npm ERR! 
npm ERR! Failed at the my-next-blog@0.1.5 start script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.

npm ERR! A complete log of this run can be found in:
npm ERR!     /home/<USER>/.npm/_logs/2022-01-12T16_29_36_245Z-debug.log

> my-next-blog@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production

> ethos-tracking@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> NODE_ENV=production & next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production

> ethos-tracking@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> NODE_ENV=production & next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production

> ethos-tracking@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> NODE_ENV=production & next start

Error: listen EADDRINUSE: address already in use 0.0.0.0:3000
    at Server.setupListenHandle [as _listen2] (net.js:1331:16)
    at listenInCluster (net.js:1379:12)
    at doListen (net.js:1516:7)
    at processTicksAndRejections (internal/process/task_queues.js:83:21) {
  code: 'EADDRINUSE',
  errno: -98,
  syscall: 'listen',
  address: '0.0.0.0',
  port: 3000
}
npm ERR! code ELIFECYCLE
npm ERR! errno 1
npm ERR! ethos-tracking@0.1.5 start: `NODE_ENV=production & next start`
npm ERR! Exit status 1
npm ERR! 
npm ERR! Failed at the ethos-tracking@0.1.5 start script.
npm ERR! This is probably not a problem with npm. There is likely additional logging output above.

npm ERR! A complete log of this run can be found in:
npm ERR!     /home/<USER>/.npm/_logs/2022-01-17T16_36_43_897Z-debug.log

> ethos-tracking@0.1.5 start /home/<USER>/snmmdrmxhc/public_html
> NODE_ENV=production & next start

ready - started server on 0.0.0.0:3000, url: http://localhost:3000
Warning: For production Image Optimization with Next.js, the optional 'sharp' package is strongly recommended. Run 'yarn add sharp', and Next.js will use it automatically for Image Optimization.
Read more: https://nextjs.org/docs/messages/sharp-missing-in-production
