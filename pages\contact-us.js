import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import Hsform from "../components/hsform"
import { fetchAPI } from "../lib/api"
import Image from "next/image"
import Link from "next/link"
import { getStrapiMedia } from "../lib/media"

const Contact = ({ pages }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(pages.attributes.headerimage)} width={pages.attributes.headerimage.data.attributes.width} height={pages.attributes.headerimage.data.attributes.height} className="img-fluid" alt="blog contact header image ethos tracking" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="contact-us-section">
                <div className="container contact-us-container">
                    <div className="row contact-us-content">
                        <div className="col-6 contact-us-item">
                            <div className="txt-holder pr-100">
                                <div dangerouslySetInnerHTML={{__html: pages.attributes.content}} />
                                <Link href="/help-center" className="btn link-btn">Frequently Asked Questions →</Link>
                            </div>
                        </div>
                        <div className="col-6 contact-us-item">
                            <Hsform />
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}

export async function getServerSideProps() {
    try {
        // Run API calls in parallel
        const pagesRes = await fetchAPI("/pages/2", {
            populate: {
                headerimage: "*",
                seo: {
                    populate: "*"
                }
            }
        })

        return {
            props: {
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching contact page data:", error);
        return {
            props: {
                pages: null,
            }
        }
    }
}

export default Contact