import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Image from 'next/image'
import Link from 'next/link'
import { getStrapiMedia } from "../lib/media"

const About = ({ pages }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(pages.attributes.headerimage)} width="1920" height="300" alt="Ethos" className="img-fluid" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section className="numbers-section">
                <div className="container numbers-container">
                    <div className="row heding-holder mb-0">
                        <div className="col-5">
                            <h2>{pages.attributes.subtitle}</h2>
                        </div>
                        <div className="col-7">
                            <div dangerouslySetInnerHTML={{__html: pages.attributes.content }}/>
                        </div>
                    </div>
                    {/* <div className="row numbers-content">
                        <div className="col-3 numbers-item">
                            <h3>2021</h3>
                            <p>Lorem ipsum dolor sit amet</p>
                        </div>
                        <div className="col-3 numbers-item">
                            <h3>5m</h3>
                            <p>Lorem ipsum dolor sit amet</p>
                        </div>
                        <div className="col-3 numbers-item">
                            <h3>100+</h3>
                            <p>Lorem ipsum dolor sit amet</p>
                        </div>
                        <div className="col-3 numbers-item">
                            <h3>67</h3>
                            <p>Lorem ipsum dolor sit amet</p>
                        </div>
                    </div> */}
                </div>
            </section>

            <div className="bg-texture">
            {(pages.attributes.sections).map((sect, i) => {
                if(i == 0){
                    return (
                        <section className="features-main-section" key={'about_sect_' + i}>
                            <div className="container features-main-container">
                                <div className="row features-main-content aic">
                                    <div className="col-6 txt-holder">
                                        <h5>{sect.smalltitle}</h5>
                                        <h3>{sect.title}</h3>
                                        <p>{sect.description}</p>
                                        <Link href={sect.url}>
                                            <a className="btn second-bg white box-shadow">{sect.button_label}</a>
                                        </Link>
                                    </div>
                                    <div className="col-6 img-holder">
                                        {
                                            (sect.image != '' && sect.image != null && sect.image.data != null) ? (
                                                <Image src={getStrapiMedia(sect.image)} alt="Video" width={sect.image.data.attributes.width} height={sect.image.data.attributes.height} className="img-fluid" />
                                            ) : (
                                                <div className="sentences">
                                                    <h3><span className="red-circle">1</span> You’re already doing the work</h3>
                                                    <h3><span className="red-circle">2</span> Walking the walk is necessary to stay competitive</h3>
                                                    <h3><span className="red-circle">3</span> The world needs fixing</h3>
                                                </div>
                                            )
                                        }
                                        {/* <div className="play-holder">
                                            <span data-fancybox="video-gallery" data-src={sect.youtube} data-caption="Now Playing: Video Title Goes Here"><span><Image src="/images/play-video-icon.svg" alt="Play" width={112} height={112} className="img-fluid" /></span></span>
                                        </div> */}
                                    </div>
                                </div>
                            </div>
                        </section>
                    )
                }
            })}


                {/* <section className="lever-section">
                    <div className="container lever-container">
                        <div className="row heding-holder mb-100">
                            <div className="col-12 text-center txt-holder">
                                <h5>OUR VALUES</h5>
                                <h3>Ethos Tracking Core Company Values</h3>
                            </div>
                        </div>
                        <div className="row lever-content">
                            <div className="col-4">
                                <div className="lever-item">
                                    <div className="icon-holder gray-bg">
                                        <Image src="/images/salary-icon.svg" alt="SUCCESS STORIES" width={26} height={30} className="img-fluid" />
                                    </div>
                                    <h3>Value</h3>
                                    <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi.<br /> Proin sem risus, aliquet in pretium eu, ultrices eu nibh.</p>
                                </div>
                            </div>
                            <div className="col-4">
                                <div className="lever-item">
                                    <div className="icon-holder gray-bg">
                                        <Image src="/images/dollar-icon.svg" alt="SUCCESS STORIES" width={17} height={30} className="img-fluid" />
                                    </div>
                                    <h3>Value</h3>
                                    <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi.<br /> Proin sem risus, aliquet in pretium eu, ultrices eu nibh.</p>
                                </div>
                            </div>
                            <div className="col-4">
                                <div className="lever-item">
                                    <div className="icon-holder gray-bg">
                                        <Image src="/images/save-money-icon.svg" alt="SUCCESS STORIES" width={31} height={31} className="img-fluid" />
                                    </div>
                                    <h3>Value</h3>
                                    <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi.<br /> Proin sem risus, aliquet in pretium eu, ultrices eu nibh.</p>
                                </div>
                            </div>
                            <div className="col-4">
                                <div className="lever-item">
                                    <div className="icon-holder gray-bg">
                                        <Image src="/images/stayhome-icon.svg" alt="SUCCESS STORIES" width={33} height={30} className="img-fluid" />
                                    </div>
                                    <h3>Value</h3>
                                    <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi.<br /> Proin sem risus, aliquet in pretium eu, ultrices eu nibh.</p>
                                </div>
                            </div>
                            <div className="col-4">
                                <div className="lever-item">
                                    <div className="icon-holder gray-bg">
                                        <Image src="/images/care-icon.svg" alt="SUCCESS STORIES" width={33} height={30} className="img-fluid" />
                                    </div>
                                    <h3>Value</h3>
                                    <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi.<br /> Proin sem risus, aliquet in pretium eu, ultrices eu nibh.</p>
                                </div>
                            </div>
                            <div className="col-4">
                                <div className="lever-item">
                                    <div className="icon-holder gray-bg">
                                        <Image src="/images/sales-icon.svg" alt="SUCCESS STORIES" width={33} height={30} className="img-fluid" />
                                    </div>
                                    <h3>Value</h3>
                                    <p>Aliquam interdum risus vitae lectus convallis sodales quis dignissim mi.<br /> Proin sem risus, aliquet in pretium eu, ultrices eu nibh.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section> */}

                <section className="answers-section mt-5 pt-5">
                    <div className="container answers-container">
                        <div className="row answers-content">
                            {(pages.attributes.levers).map((lever, i) => {
                                if(i == 0){
                                    return (
                                        <div className="col-6 img-container" key={'about_lever_' + i}>
                                            <div className="img-wrapper">
                                                <div className="img-holder">
                                                    <Image src={getStrapiMedia(lever.image)} alt="Emily Kane Miller" width={lever.image.data.attributes.width} height={lever.image.data.attributes.height} className="img-fluid author-img" />

                                                    <div className="heading-holder">
                                                        <h3>{lever.title}</h3>
                                                        <h5>{lever.description}</h5>
                                                    </div>
                                                </div>
                                                <div className="icon-holder">
                                                    <Image src="/images/circle-icon.svg" alt="Emily Kane Miller" width={60} height={60} className="img-fluid" />
                                                </div>
                                            </div>
                                        </div>
                                    )
                                }else if(i == 1){
                                    return (
                                        <div className="col-6 txt-holder" key={'about_lever_' + i}>
                                            <h5>{lever.title}</h5>
                                            <h3>Ethos Tracking Vision</h3>
                                            <p>{lever.description}</p>
                                            <p><Image src={getStrapiMedia(lever.image)} alt="Emily Kane Miller signature" width={lever.image.data.attributes.width} height={lever.image.data.attributes.height} className="img-fluid author-img" /></p>
                                        </div>
                                    )
                                }
                            })}
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </Layout>
  )
}


export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/1", {
            populate: {
                sections: {
                    populate: {
                        image: "*"
                    }
                },
                levers: {
                    populate: {
                        image: "*"
                    }
                },
                headerimage: {
                    populate: "*"
                },
                seo: {
                    populate: "*"
                }
            }
        })

        return {
            props: {
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching about-us page data:", error);
        return {
            props: {
                pages: null,
            }
        }
    }
}

export default About