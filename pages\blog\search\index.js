import Postcard from "../../../components/postcard"
import { fetchAPI } from "../../../lib/api"
import Layout from "../../../components/layout"
import Seo from "../../../components/seo"
import SearchForm from "../../../components/search"
import { useRouter } from "next/router";
import Image from "next/image"

const Search = ({ posts }) => {
    const router = useRouter();
    const seo = {
        metaTitle: "Search results",
        metaDescription: `All search result posts`,
    }
    return (
        <Layout>
            <Seo seo={seo} />
            <main>
                <section className="main-img-section">
                    <div className="container-fluid main-img-container">
                        <div className="row main-img-content">
                            <div className="col-12 main-img-item">
                                <div className="bg-holder">
                                    <Image src={"/images/main-img.jpg"} alt="Video" className="img-fluid" width="1920" height="300" />
                                </div>
                                <div className="heading-holder">
                                    <div className="container">
                                        <h1>Blog</h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <section className="main-blog-section">
                    <div className="container filter-container mb-100">
                        <div className="row filter-content">
                            <div className="col-6 filter-heading py-5">
                                <h2>Search Result For <span className="red">&quot;{router.query.term}&quot;</span></h2>
                            </div>
                            <ul className="col-6 filter-items">
                                <SearchForm type={'blog'} />
                            </ul>
                        </div>
                    </div>
                    <div className="container blog-group-container mb-150">
                        <div className="row blog-group-content">
                            {posts.map((post, i) => {
                                return (
                                    <Postcard
                                        post={post}
                                        key={`postcard_key_${i}`}
                                    />
                                )
                            })}
                        </div>
                    </div>
                </section>
            </main>
        </Layout>
    )
}

export async function getServerSideProps({ query }) {
    const results = await fetchAPI("/posts", {
        filters: {
            title: {
                $containsi: query.term
            }
        },
        populate: "*"
    })

    return {
        props: {
            posts: results.data
        }
    }
}

export default Search