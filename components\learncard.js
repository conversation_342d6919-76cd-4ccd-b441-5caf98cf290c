import React from "react"
import Image from "next/image"
import Link from "next/link"
import Moment from "react-moment"
import { getStrapiMedia } from "../lib/media"

const Learncard = ({ post }) => {
  return (
    <Link href={`/videos/${post.attributes.slug}`} className="col-4" style={{cursor: "pointer"}}>
        <div className="blog-group-item">
            <div className="img-holder" style={{position: "relative", width: "100%", height: "250px", overflow: "hidden"}}>
                {post.attributes.image?.data ? (
                    <Image
                        src={getStrapiMedia(post.attributes.image)}
                        className="img-fluid"
                        alt="learn image ethos tracking"
                        fill
                        style={{objectFit: "cover"}}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                ) : (
                    <Image
                        src="/images/main-img.jpg"
                        alt="Default learn image"
                        fill
                        className="img-fluid"
                        style={{objectFit: "cover"}}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                )}
                <div className="play-holder">
                    <span><Image src="/images/play-video-icon.svg" alt="play ethos tracking" width="112" height="112" className="img-fluid" /></span>
                </div>
            </div>
            <div className="txt-holder">
                <h3>{post.attributes.title}</h3>
            </div>
        </div>
    </Link>
  )
}

export default Learncard
