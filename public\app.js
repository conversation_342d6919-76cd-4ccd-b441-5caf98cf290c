// TEXTAREA auto height resize on type
!function(e,t){if("function"==typeof define&&define.amd)define(["module","exports"],t);else if("undefined"!=typeof exports)t(module,exports);else{var n={exports:{}};t(n,n.exports),e.autosize=n.exports}}(this,function(e,t){"use strict";var n,o,p="function"==typeof Map?new Map:(n=[],o=[],{has:function(e){return-1<n.indexOf(e)},get:function(e){return o[n.indexOf(e)]},set:function(e,t){-1===n.indexOf(e)&&(n.push(e),o.push(t))},delete:function(e){var t=n.indexOf(e);-1<t&&(n.splice(t,1),o.splice(t,1))}}),c=function(e){return new Event(e,{bubbles:!0})};try{new Event("test")}catch(e){c=function(e){var t=document.createEvent("Event");return t.initEvent(e,!0,!1),t}}function r(r){if(r&&r.nodeName&&"TEXTAREA"===r.nodeName&&!p.has(r)){var e,n=null,o=null,i=null,d=function(){r.clientWidth!==o&&a()},l=function(t){window.removeEventListener("resize",d,!1),r.removeEventListener("input",a,!1),r.removeEventListener("keyup",a,!1),r.removeEventListener("autosize:destroy",l,!1),r.removeEventListener("autosize:update",a,!1),Object.keys(t).forEach(function(e){r.style[e]=t[e]}),p.delete(r)}.bind(r,{height:r.style.height,resize:r.style.resize,overflowY:r.style.overflowY,overflowX:r.style.overflowX,wordWrap:r.style.wordWrap});r.addEventListener("autosize:destroy",l,!1),"onpropertychange"in r&&"oninput"in r&&r.addEventListener("keyup",a,!1),window.addEventListener("resize",d,!1),r.addEventListener("input",a,!1),r.addEventListener("autosize:update",a,!1),r.style.overflowX="hidden",r.style.wordWrap="break-word",p.set(r,{destroy:l,update:a}),"vertical"===(e=window.getComputedStyle(r,null)).resize?r.style.resize="none":"both"===e.resize&&(r.style.resize="horizontal"),n="content-box"===e.boxSizing?-(parseFloat(e.paddingTop)+parseFloat(e.paddingBottom)):parseFloat(e.borderTopWidth)+parseFloat(e.borderBottomWidth),isNaN(n)&&(n=0),a()}function s(e){var t=r.style.width;r.style.width="0px",r.offsetWidth,r.style.width=t,r.style.overflowY=e}function u(){if(0!==r.scrollHeight){var e=function(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push({node:e.parentNode,scrollTop:e.parentNode.scrollTop}),e=e.parentNode;return t}(r),t=document.documentElement&&document.documentElement.scrollTop;r.style.height="",r.style.height=r.scrollHeight+n+"px",o=r.clientWidth,e.forEach(function(e){e.node.scrollTop=e.scrollTop}),t&&(document.documentElement.scrollTop=t)}}function a(){u();var e=Math.round(parseFloat(r.style.height)),t=window.getComputedStyle(r,null),n="content-box"===t.boxSizing?Math.round(parseFloat(t.height)):r.offsetHeight;if(n<e?"hidden"===t.overflowY&&(s("scroll"),u(),n="content-box"===t.boxSizing?Math.round(parseFloat(window.getComputedStyle(r,null).height)):r.offsetHeight):"hidden"!==t.overflowY&&(s("hidden"),u(),n="content-box"===t.boxSizing?Math.round(parseFloat(window.getComputedStyle(r,null).height)):r.offsetHeight),i!==n){i=n;var o=c("autosize:resized");try{r.dispatchEvent(o)}catch(e){}}}}function i(e){var t=p.get(e);t&&t.destroy()}function d(e){var t=p.get(e);t&&t.update()}var l=null;"undefined"==typeof window||"function"!=typeof window.getComputedStyle?((l=function(e){return e}).destroy=function(e){return e},l.update=function(e){return e}):((l=function(e,t){return e&&Array.prototype.forEach.call(e.length?e:[e],function(e){return r(e)}),e}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],i),e},l.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],d),e}),t.default=l,e.exports=t.default});

autosize(document.querySelectorAll('textarea'));

// IF ELEMENT IS IN VIEWPORT - example: if($('.element').isInViewport()){ .....
$.fn.isInViewport = function() {
    if($(this).length){
    var elementTop = $(this).offset().top;
    var elementBottom = elementTop + $(this).outerHeight();
    var viewportTop = $(window).scrollTop();
    var viewportBottom = viewportTop + $(window).height();
        return elementBottom > viewportTop && elementTop < viewportBottom;
    }else{
        return false;
    }
};
$.fn.extend({
    toggleText: function(a, b){
        return this.text(this.text() == b ? a : b);
    }
});

// NOTIFICATIONS
function app_msg(msg, type='success', dur=2500){
    $('.msg-popup p').html(msg);
    $('.msg-popup').removeClass('danger info success warning');
    $('.msg-popup').addClass(type);
    $('.overlay').addClass('opened');
    $('.msg-popup').addClass('showw');
    if(dur != 0){
        setTimeout(function(){
            $('.overlay').removeClass('opened');
            $('.msg-popup').removeClass('showw');
            $('.msg-popup').removeClass('center-popup');
        },dur);
    }else{
        $('.close_popup').show();
    }
}
$('.close_popup').on('click', function(){
    $('.msg-popup').removeClass('showw');
});
$('.msg-popup').on('click', function(e){
    e.stopPropagation();
});

// COPY LINK TO CLIPBOARD
function copyToClipboard(text) {
    var inputc = document.body.appendChild(document.createElement("input"));
    inputc.value = window.location.href;
    inputc.focus();
    inputc.select();
    document.execCommand('copy');
    inputc.parentNode.removeChild(inputc);
    $('html, body').scrollTop(250);
    app_msg("URL Copied.");
}
// ADD CLASS ON SCROLL
$(document).on('scroll', function(){
    // if($('body').hasClass('homepage')){
        if($(window).scrollTop() > 200){
            $('body').removeClass('transparent');
        }else{
            $('body').addClass('transparent');
        }
    // }
});

// DROPDOWN - markup
// <div class="dropdown">
//     <a href="...." data-dropdown>LINK</a>
//     <div class="dropdown-menu">
//         <div class="dropdown">
// 	       <div class="dropdown">
//      </div>
// </div>
$('[data-dropdown]').click(function (e) {
    e.stopPropagation();
    let xx = $(this);
    if (xx.parent().hasClass('opened')) {
        $('.dropdown').removeClass('opened');
        xx.parent().removeClass('opened');
    } else {
        $('.dropdown').removeClass('opened');
        xx.parent().addClass('opened');
    }
});

// CLOSE ALL POPUPS & DROPDOWNS on sideways click
$('html, body, .close_page, .close').click(function () {
    close_all()
});

function close_all() {
    $('body').removeClass('show-popup');
    $('.popup').removeClass('show');
    $('body').removeClass('menu-opened');
    $('.dropdown').removeClass('opened');
    $('.menu-dropdown').removeClass('opened');
    $('.login-dropdown').removeClass('opened');
    $('.reg-dropdown').removeClass('opened');
    $('.reg-button').removeClass('red-bg');
    $('.login-button').removeClass('red');
    $('.menu-button').removeClass('active');
};

// MOBILE MENU
$('.menu-button').click(function (e) {
    e.stopPropagation();
    if ($(this).hasClass('active')) {
        close_all();
    } else {
        $('body').removeClass('show-popup');
        $('.login-dropdown').removeClass('opened');
        $('.reg-dropdown').removeClass('opened');
        $('.reg-button').removeClass('red-bg');
        $('.login-button').removeClass('red');
        $(this).addClass('active');
        $('.menu-dropdown').addClass('opened');
        $('body').addClass('menu-opened');
    }
});

// REVEAL PASSWORD - eye icon
$(".reveal_password").click(function () {
    $(this).toggleClass('active');
    var input = $(this).next();
    if (input.attr("type") == "password") {
        input.attr("type", "text");
    } else {
        input.attr("type", "password");
    }
});

// ANCHOR animation scroll to element on page
$('[data-anchor]').click(function(){
    var xx = $(this).data('anchor');
    $('html, body').animate({scrollTop: $(xx).offset().top - 30 - $('header').height()});
});

// FAQ
$('.faq-list-one h2').click(function(){
    if($(this).parent().hasClass('active')){
        // $(this).find('span').text('+');
        // $('.faq-list-one h2').find('i').removeClass('icon-rotated');
        $(this).find('i').removeClass('icon-rotated');
        $(this).closest('.faq-list-one').removeClass('active');
        $(this).next().slideUp();
    }else{
        $('.faq-content').slideUp();
        $('.faq-list-one').removeClass('active');
        $('.faq-list-one h2').find('i').removeClass('icon-rotated');
        $(this).find('i').addClass('icon-rotated');
        $(this).closest('.faq-list-one').addClass('active');
        $(this).next().slideDown();
    }
});

// SWIPER - check if element exists, then initialize script
if($(".homepage-sponsors").length){
    var swiper = new Swiper(".homepage-sponsors", {
        slidesPerView: 3,
        spaceBetween: 10,
        pagination: {
            el: ".homepage-sponsors-pagination",
            clickable: true,
        },
        breakpoints: {
            320: {
                slidesPerView: 1,
                spaceBetween: 20,
            },
            640: {
                slidesPerView: 1,
                spaceBetween: 20,
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 40,
            },
            1024: {
                slidesPerView: 3,
                spaceBetween: 50,
            },
        }
    });
}

// AJAX
$('#login-form').on('submit', function (e) {
    e.preventDefault();
    console.log('login-form');
    var form = $(this);
    var url = form.attr('action');
	var button = form.find('button[type=submit]');
    button.addClass('btn--loading');
    $.ajax({
        type: "POST",
        url: url,
        data: form.serialize(),
        dataType: "json",
        success: function(data){
            console.log(data);
            if(data.success){
                // $(".login_error").hide();
                console.log('SUCCESS');
                app_msg('SUCCESS');
                console.log(data);
                window.location.href = data.return_url;
            } else {
                console.log('NO SUCCESS');
                app_msg(data.error, 'danger');
                setTimeout(function(){
                    button.removeClass('btn--loading');
                },800);
            }
        },
        error: function(result){
            console.log('ERROR WITH PHP');
            console.log(result);
            app_msg("Server error. Please try again");
            setTimeout(function(){
                button.removeClass('btn--loading');
            },800);
        }
    });
});

// SHOW IMAGE PREVIEW for input type=file
$("#image").change(function(){
    console.log('#IMAGE change');
    readURL(this);
});
function readURL(input) {
    var parent = $(input).closest('.upload-image').parent();
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
            parent.find('.image_preview').attr('src', e.target.result).addClass('has_image');
            $('#image_removed').val(0);
            // $('#main_form button[type=submit]').removeClass('green-bg').addClass('red-bg').text(initial_button_text).attr('disabled', false);
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// POPUP
$('[data-popup]').on('click',  function (e) {
	e.stopPropagation();
	e.preventDefault();
	console.log($(this).data('return_url'));
	if($(this).data('return_url')){
		$('[name="return_url"]').val($(this).data('return_url'));
	}
	close_all();
	var popup = $(this).data('popup');
	console.log(popup);

	$('body').addClass('show-popup');
	$('.' + popup).addClass('show');
});
$('.popup').click(function (e) {
	e.stopPropagation();
});

// accordion
$('#accordion').find('.accordion-toggle').click(function(){
    $(this).toggleClass('active');
    $(".accordion-toggle").not($(this)).removeClass('active');
    $(this).next().slideToggle('fast');
    $(".accordion-content").not($(this).next()).slideUp('fast');
});