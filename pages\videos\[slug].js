import Moment from "react-moment"
import { fetchAPI } from "../../lib/api"
import { getStrapiMedia } from "../../lib/media"
import Layout from "../../components/layout"
import Seo from "../../components/seo"
import Image from "next/image"
import Link from "next/link"
import Script from "next/script"
import { FacebookShareButton, LinkedinShareButton, TwitterShareButton, EmailShareButton } from 'next-share'
import { useRouter } from 'next/router'
import getConfig from 'next/config'

const Post = ({ post }) => {
  const seo = {
    metaTitle: post.attributes.title,
    metaDescription: post.attributes.description,
    shareImage: post.attributes.image,
    post: true,
  }
  const { asPath, pathname } = useRouter();
  const { publicRuntimeConfig } = getConfig();

  const copyText = (entryText) => {
    navigator.clipboard.writeText(entryText);
    alert('COPIED!');
  }

  return (
    <Layout>
        <Seo seo={seo} />
        <main>
            <ul className="social-icons-content">
                <li className="social-icons-item">
                    <FacebookShareButton url={ publicRuntimeConfig.baseUrl + asPath } quote={post.attributes.title} hashtag={'#ethostracking'}>
                        <span>
                            <Image src="/images/fb-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" />
                        </span>
                    </FacebookShareButton>
                </li>
                <li className="social-icons-item">
                    <LinkedinShareButton url={ publicRuntimeConfig.baseUrl + asPath } title={post.attributes.title} >
                        <span>
                            <Image src="/images/in-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" />
                        </span>
                    </LinkedinShareButton>
                </li>
                <li className="social-icons-item">
                    <TwitterShareButton url={ publicRuntimeConfig.baseUrl + asPath } title={post.attributes.title} >
                        <span>
                            <Image src="/images/tw-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" />
                        </span>
                    </TwitterShareButton>
                </li>
                <li className="social-icons-item">
                    <EmailShareButton url={ publicRuntimeConfig.baseUrl + asPath } subject={'Ethos Tracking new post: ' + post.attributes.title} >
                        <span>
                            <Image src="/images/email-share-icon.svg" alt="Facebook" className="img-fluid" width="11" height="23" body={ publicRuntimeConfig.baseUrl + asPath }/>
                        </span>
                    </EmailShareButton>
                </li>
                <li className="social-icons-item">
                    <span onClick={() => copyText(publicRuntimeConfig.baseUrl + asPath)}><Image src="/images/copy-share-icon.svg" alt="Copy" className="img-fluid" width="11" height="23" /></span>
                </li>
            </ul>

            <section className="blog-full-section pt-5">
                <div className="container mw-1000 blog-full-container">
                    <div className="row blog-full-content features-main-section" style={{padding: 0}}>
                        <div className="col-12 main-img-content mb-100 features-main-content" data-fancybox="" data-src={post.attributes.youtube} data-caption={post.attributes.title} style={{cursor: "pointer"}}>
                            <div className="img-holder" style={{position: "relative", width: "100%", height: "400px", overflow: "hidden", borderRadius: "8px"}}>
                                {post.attributes.image?.data ? (
                                    <Image
                                        src={getStrapiMedia(post.attributes.image)}
                                        className="img-fluid w-100"
                                        alt="learn image ethos tracking"
                                        fill
                                        style={{objectFit: "cover"}}
                                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1000px"
                                    />
                                ) : (
                                    <Image
                                        src="/images/main-img.jpg"
                                        alt="Default video image"
                                        fill
                                        className="img-fluid"
                                        style={{objectFit: "cover"}}
                                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1000px"
                                    />
                                )}
                                <div className="play-holder">
                                    <span>
                                        <Image
                                            src="/images/play-video-icon.svg"
                                            alt="play ethos tracking"
                                            width="112"
                                            height="112"
                                            className="img-fluid"
                                        />
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className="col-12 heading-content mb-5">
                            <h1>{post.attributes.title}</h1>
                        </div>
                        <div className="col-12 blog-full-item">
                            <div className="txt-editor-content mb-5">
                                <div dangerouslySetInnerHTML={{ __html: post.attributes.description }} />
                            </div>
                            <div className="btn-holder">
                                <Link href="/videos" className="btn border-btn fs-14">BACK</Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}

// export async function getStaticPaths() {
//   const postsRes = await fetchAPI("/learns", { fields: ["slug"] })

//   return {
//     paths: postsRes.data.map((post) => ({
//       params: {
//         slug: post.attributes.slug,
//       },
//     })),
//     fallback: true,
//   }
// }

// export async function getStaticProps({ params }) {
//     const postsRes = await fetchAPI("/learns", {
//         filters: {
//             slug: params.slug,
//         },
//         populate: "*",
//     })

//     return {
//         props: {
//             post: postsRes.data[0],
//         },
//         revalidate: 1,
//     }
// }

export async function getServerSideProps({ params }) {
  const postsRes = await fetchAPI("/learns", {
    filters: {
      slug: params.slug,
    },
    populate: "*",
  });

  return {
    props: {
      post: postsRes.data[0],
    },
  };
}

export default Post
