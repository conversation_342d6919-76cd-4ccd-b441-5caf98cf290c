import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Image from 'next/image';
import Link from 'next/link';

const Thankyou = ({ pages }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="thank-you-section">
                <div className="container-fluid thank-you-container">
                    <div className="row thank-you-content">
                        <div className="col-12 thank-you-item">
                            <div className="bg-holder">
                                <Image src="/images/index-header.jpg" width="1920" height="850" alt="Thank you" className="img-fluid" />
                            </div>
                            <div className="txt-holder">
                                <h1>{pages.attributes.title}</h1>
                                <p>{pages.attributes.subtitle}</p>
                                <Link href="/" className="btn second-bg white">GO BACK HOME</Link>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}

export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/9", {
            populate: {
                populate: {
                    seo: {
                        populate: "*"
                    }
                }
            }
        })

        return {
            props: {
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching thank you page data:", error);
        return {
            props: {
                pages: null,
            }
        }
    }
}

export default Thankyou