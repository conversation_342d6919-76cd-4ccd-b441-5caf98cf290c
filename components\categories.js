import React from "react"
import Link from "next/link"

const Categories = ({ category, current, type }) => {
  return (
    <li className={"filter-item " + category.attributes.slug}>
        <Link href={`/${type}/category/${category.attributes.slug}`} className={" btn border-btn text-uppercase" + (current == category.attributes.slug ? 'active second-bg box-shadow white' : '') + ""}>{category.attributes.name}</Link>
    </li>
  )
}

export default Categories
