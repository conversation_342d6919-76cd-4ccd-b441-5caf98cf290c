let slideUp = (target, duration=500) => {
    target.style.transitionProperty = 'height, margin, padding';
    target.style.transitionDuration = duration + 'ms';
    target.style.boxSizing = 'border-box';
    target.style.height = target.offsetHeight + 'px';
    target.offsetHeight;
    target.style.overflow = 'hidden';
    target.style.height = 0;
    target.style.paddingTop = 0;
    target.style.paddingBottom = 0;
    target.style.marginTop = 0;
    target.style.marginBottom = 0;
    window.setTimeout( () => {
        target.style.display = 'none';
        target.style.removeProperty('height');
        target.style.removeProperty('padding-top');
        target.style.removeProperty('padding-bottom');
        target.style.removeProperty('margin-top');
        target.style.removeProperty('margin-bottom');
        target.style.removeProperty('overflow');
        target.style.removeProperty('transition-duration');
        target.style.removeProperty('transition-property');
    }, duration);
}

let slideDown = (target, duration=500) => {
    target.style.removeProperty('display');
    let display = window.getComputedStyle(target).display;

    if (display === 'none')
    display = 'block';

    target.style.display = display;
    let height = target.offsetHeight;
    target.style.overflow = 'hidden';
    target.style.height = 0;
    target.style.paddingTop = 0;
    target.style.paddingBottom = 0;
    target.style.marginTop = 0;
    target.style.marginBottom = 0;
    target.offsetHeight;
    target.style.boxSizing = 'border-box';
    target.style.transitionProperty = "height, margin, padding";
    target.style.transitionDuration = duration + 'ms';
    target.style.height = height + 'px';
    target.style.removeProperty('padding-top');
    target.style.removeProperty('padding-bottom');
    target.style.removeProperty('margin-top');
    target.style.removeProperty('margin-bottom');
    window.setTimeout( () => {
        target.style.removeProperty('height');
        target.style.removeProperty('overflow');
        target.style.removeProperty('transition-duration');
        target.style.removeProperty('transition-property');
    }, duration);
}
var slideToggle = (target, duration = 500) => {
    if(window.getComputedStyle(target).display === 'none'){
        return slideDown(target, duration);
    }else{
        return slideUp(target, duration);
    }
}
[].forEach.call(document.querySelectorAll(".mobile-menu-button"), function(el) {
    el.addEventListener("click", function() {
        document.querySelector("body").classList.add("menu-opened");
        document.querySelector(".mobile-menu-button").classList.add("active");
    });
});
[].forEach.call(document.querySelectorAll(".link-item"), function(el) {
    el.addEventListener("click", function() {
        document.querySelector("body").classList.remove("menu-opened");
        document.querySelector(".mobile-menu-button").classList.remove("active");
    });
});
[].forEach.call(document.querySelectorAll(".search-btn"), function(el) {
    el.addEventListener("click", function() {
        document.getElementById("search-container").classList.toggle("active");
    });
});
[].forEach.call(document.querySelectorAll(".accordion-toggle"), function(el) {
    el.addEventListener("click", function() {
        el.classList.toggle('active');
        [].forEach.call(document.querySelectorAll(".accordion-toggle"), function(elem) {
            if(elem != el){
                elem.classList.remove('active');
            }
        })
        slideToggle(el.nextElementSibling);
        [].forEach.call(document.querySelectorAll(".accordion-content"), function(elemm) {
            if(elemm != el.nextElementSibling){
                slideUp(elemm, 200);
            }
        })
    });
});
