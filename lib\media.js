import { getStrapiURL } from "./api"

export function getStrapiMedia(media) {
  // Handle cases where media might be null/undefined or missing data
  if (!media || !media.data || !media.data.attributes || !media.data.attributes.url) {
    return "/images/main-img.jpg" // Return a placeholder image
  }

  const { url } = media.data.attributes
  const imageUrl = url.startsWith("/") ? getStrapiURL(url) : url
  return imageUrl
}
