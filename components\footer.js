import React from "react"
import Link from "next/link"
import Image from "next/image"
import Script from 'next/script';
import { useContext } from "react"
import { GlobalContext } from "../pages/_app"

const Footer = () => {
    const { social } = useContext(GlobalContext)

    return (
        <div>
            <div className="overlay"></div>
            <div className="msg-popup">
                <span className="close_popup" style={{display: "none"}}>×</span>
                <p className="m-0">Your email has been sent</p>
            </div>
            <footer>
                <div className="container">
                    <div className="row footer-cta-content">
                        <div className="col-8 footer-cta-txt">
                            <h3>Ready to level up your data?</h3>
                            <p>Let Ethos Tracking show you how.</p>
                        </div>
                        <div className="col-4 flex jcr">
                            <div className="footer-cta-btn">
                                <Image src="/images/footer-cta-img.jpg" alt="" width="285" height="170" className="img-fluid" />
                                <Link href="/contact-us" className="btn second-bg white box-shadow">GET STARTED</Link>
                            </div>
                        </div>
                    </div>
                    <div className="row footer-links-content">
                        <div className="col-2 footer-links-item">
                            <div className="logo-holder">
                                <Link href={"/"}><Image src="/images/footer-logo.svg" width="161" height="63" alt="Ethos" className="img-fluid" /></Link>
                            </div>
                            <ul className="social-icons-holder">
                                <li>
                                    <Link href={social.facebook && (social.facebook)} target="_blank"><Image src="/images/facebook-icon.svg" alt="Facebook" width="40" height="40" className="img-fluid" /></Link>
                                </li>
                                <li>
                                    <Link href={social.instagram && (social.instagram)} target="_blank"><Image src="/images/instagram-icon.svg" alt="Instagram" width="40" height="40" className="img-fluid" /></Link>
                                </li>
                                <li>
                                    <Link href={social.linkedin && (social.linkedin)} target="_blank"><Image src="/images/linkedin-icon.svg" alt="linkedin" width="40" height="40" className="img-fluid" /></Link>
                                </li>
                            </ul>
                        </div>
                        <div className="col-2 footer-links-item">
                            <ul>
                                <li>
                                    <Link href="/about-us">About</Link>
                                </li>
                                <li>
                                    <Link href="/features">Features/Overview</Link>
                                </li>
                                <li>
                                    <Link href="/pricing">Pricing</Link>
                                </li>
                                <li>
                                    <Link href="/contact-us">Get Started</Link>
                                </li>
                            </ul>
                        </div>
                        <div className="col-2 footer-links-item">
                            <h5 style={{ marginBottom: "15px" }}>Resources:</h5>
                            <ul>
                                <li>
                                    <Link href="/blog">Blog</Link>
                                </li>
                                <li>
                                    <Link href="/learn">Learn</Link>
                                </li>
                                <li>
                                    <Link href="/careers">Careers</Link>
                                </li>
                            </ul>
                        </div>
                        <div className="col-2 footer-links-item">
                            <ul>
                                <li>
                                    <Link href="/help-center">FAQ</Link>
                                </li>
                                <li>
                                    <Link href="/contact-us">Contact Sales</Link>
                                </li>
                                <li>
                                    <Link href="/accessibility">Accessibility Statement</Link>
                                </li>
                                <li>
                                    <Link href="/privacy-policy">Privacy Policy</Link>
                                </li>
                            </ul>
                        </div>
                        <div className="col-4 footer-links-item flex aic jcc">
                            <div className="flex aic jcr w100">
                                <div className="gp-logo">
                                    <a href="https://app.greenplaces.com/sustainability/ethos-giving" target="_blank" rel="noreferrer">
                                        <Image src="/images/gp_badge_2024_light_solid.png" width={166} height={140} alt="b-corp logo"/>
                                    </a>
                                </div>
                                <div className="bcorp-logo">
                                    <a href="https://www.bcorporation.net/en-us/find-a-b-corp/company/ethos-giving" target="_blank"  rel="noreferrer"><Image src="/images/B-Corp-Logo-Black-RGB.svg" width={150} height={200} alt="b-corp logo" /></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="row footer-cta-content">
                        <div className="col-6 presale-cta">
                            {/* <h3>Presale Questions? <Link href="/contact-us"><a>Click Here</a></Link></h3> */}
                        </div>
                        <div className="col-6 all-right-txt">
                            <p>Ethos Tracking copyright 2023. All Rights Reserved.</p>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    )
}

export default Footer
