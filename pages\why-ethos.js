import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Image from 'next/image'

const Accessibility = ({ pages }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="why-section mb-0">
                <div className="container why-container mw-1240">
                    <div className="row">
                        <div className="col-4 order-mob-2">
                            <div className="mb-4">
                                <Image src="/images/why-image-1.jpg" width="330" height="505" alt="Ethos" className="img-fluid" />
                            </div>
                            <Image src="/images/why-image-2.jpg" width="330" height="421" alt="Ethos" className="img-fluid" />
                        </div>
                        <div className="col-8">
                            <h1>{pages.attributes.title}</h1>
                            <div className="txt-editor-content">
                                <div dangerouslySetInnerHTML={{ __html: pages.attributes.content}} />
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="sections-section pt-150 pb-150 lightGray-bg">
                <div className="container">
                    <div className="row">
                        <div className="col-12">
                        {(pages.attributes.sections).map((sect, i) => {
                            return(
                                <div className="white-panel" key={'sect_' + i}>
                                    <div className="white-panel-icon">{i+1}</div>
                                    <div className="white-panel-desc">
                                        <h2 className="white-panel-title">{sect.title}</h2>
                                        <p className="white-panel-content">{sect.description}</p>
                                    </div>
                                </div>
                            )
                        })}
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}

export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/13", {
            populate: {
                fields: "*",
                populate: {
                    seo: {
                        populate: "*"
                    }
                },
                sections: {
                    populate: "*"
                }
            }
        })

        return {
            props: {
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching why-ethos page data:", error);
        return {
            props: {
                pages: null,
            }
        }
    }
}

export default Accessibility