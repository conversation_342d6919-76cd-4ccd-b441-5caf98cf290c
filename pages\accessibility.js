import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"

const Accessibility = ({ pages }) => {
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="accessibility-section mb-150">
                <div className="container accessibility-container mw-1000">
                    <div className="row accessibility-content">
                        <div className="col-12 heading-content">
                            <h1>{pages.attributes.title}</h1>
                            <p>{pages.attributes.subtitle}</p>
                        </div>
                        <div className="col-12 accessibility-item">
                            <div className="txt-editor-content">
                                <div dangerouslySetInnerHTML={{ __html: pages.attributes.content }} />
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}


export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/4", {
            populate: {
                fields: "*",
                populate: {
                    seo: {
                        populate: "*"
                    }
                }
            }
        })

        return {
            props: {
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching accessibility page data:", error);
        return {
            props: {
                pages: null,
            }
        }
    }
}

export default Accessibility