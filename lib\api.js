import qs from "qs"

/**
 * Get full Strapi URL from path
 * @param {string} path Path of the URL
 * @returns {string} Full Strapi URL
 */
export function getStrapiURL(path = "") {
  return `${
    process.env.NEXT_PUBLIC_STRAPI_API_URL || "https://phpstack-1462486-5579835.cloudwaysapps.com"
  }${path}`
}

/**
 * Helper to make GET requests to Strapi API endpoints
 * @param {string} path Path of the API route
 * @param {Object} urlParamsObject URL params object, will be stringified
 * @param {Object} options Options passed to fetch
 * @returns Parsed API call response
 */
export async function fetchAPI(path, urlParamsObject = {}, options = {}) {
  // Merge default and user options
  const mergedOptions = {
    headers: {
      "Content-Type": "application/json",
    },
    ...options,
  }

  // Build request URL
  const queryString = qs.stringify(urlParamsObject)
  const requestUrl = `${getStrapiURL(
    `/api${path}${queryString ? `?${queryString}` : ""}`
  )}`

  try {
    // Trigger API call
    const response = await fetch(requestUrl, mergedOptions);
    
    if (!response.ok) {
      console.error(`API error: ${response.status} for ${requestUrl}`);
      
      // Return mock data structure based on the path
      if (path.includes("/categories")) {
        return { data: [] };
      } else if (path.includes("/posts")) {
        return { data: [] };
      } else if (path.includes("/videos")) {
        return { data: [] };
      } else if (path.includes("/stories")) {
        return { data: [] };
      } else if (path.includes("/faqs")) {
        return { data: [] };
      } else if (path === "/global") {
        return {
          data: {
            id: 1,
            attributes: {
              favicon: { data: null },
              defaultSeo: {
                metaTitle: "Ethos Tracking",
                metaDescription: "Ethos Tracking System",
                shareImage: { data: null }
              },
              social: []
            }
          }
        };
      } else if (path.includes("/homepage")) {
        return {
          data: {
            attributes: {
              hero: { data: null },
              seo: { data: null },
              home_sections: { data: [] }
            }
          }
        };
      }
      
      // Default fallback for any other path
      return { data: null };
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Failed to fetch API: ${requestUrl}`, error);
    
    // Return mock data structure based on the path
    if (path.includes("/categories")) {
      return { data: [] };
    } else if (path.includes("/posts")) {
      return { data: [] };
    } else if (path.includes("/videos")) {
      return { data: [] };
    } else if (path.includes("/stories")) {
      return { data: [] };
    } else if (path.includes("/faqs")) {
      return { data: [] };
    } else if (path === "/global") {
      return {
        data: {
          id: 1,
          attributes: {
            favicon: { data: null },
            defaultSeo: {
              metaTitle: "Ethos Tracking",
              metaDescription: "Ethos Tracking System",
              shareImage: { data: null }
            },
            social: []
          }
        }
      };
    } else if (path.includes("/homepage")) {
      return {
        data: {
          attributes: {
            hero: { data: null },
            seo: { data: null },
            home_sections: { data: [] }
          }
        }
      };
    }
    
    // Default fallback for any other path
    return { data: null };
  }
}
