/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ["phpstack-1462486-5579835.cloudwaysapps.com"],
    unoptimized: true,
  },
  serverRuntimeConfig: {
    mySecret: 'secret',
  },
  publicRuntimeConfig: {
    baseUrl: 'https://www.ethostracking.com',
  },
  generateBuildId: async () => {
    return process.env.COMMIT_REF || 'development'
  },
  // For Netlify deployment
  trailingSlash: true,
  // Add this to ignore ESLint errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable tracing to fix Windows permission issues
  experimental: {
    largePageDataBytes: 400 * 1000, // Increased from 128KB to 400KB
    instrumentationHook: false,
  },
  // Disable strict mode for now to avoid double renders
  reactStrictMode: false,
  // Disable telemetry and tracing
  telemetry: false,
  // Webpack configuration for better builds
  webpack: (config, { isServer, dev }) => {
    // Optimize for production builds
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }

    // Disable webpack's built-in tracing on Windows
    if (process.platform === 'win32') {
      config.infrastructureLogging = {
        level: 'error',
      };
    }

    return config;
  },
}

module.exports = nextConfig
