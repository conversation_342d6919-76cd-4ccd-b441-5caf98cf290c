let slideUp = (target, duration=500) => {
    target.style.transitionProperty = 'height, margin, padding';
    target.style.transitionDuration = duration + 'ms';
    target.style.boxSizing = 'border-box';
    target.style.height = target.offsetHeight + 'px';
    target.offsetHeight;
    target.style.overflow = 'hidden';
    target.style.height = 0;
    target.style.paddingTop = 0;
    target.style.paddingBottom = 0;
    target.style.marginTop = 0;
    target.style.marginBottom = 0;
    window.setTimeout( () => {
        target.style.display = 'none';
        target.style.removeProperty('height');
        target.style.removeProperty('padding-top');
        target.style.removeProperty('padding-bottom');
        target.style.removeProperty('margin-top');
        target.style.removeProperty('margin-bottom');
        target.style.removeProperty('overflow');
        target.style.removeProperty('transition-duration');
        target.style.removeProperty('transition-property');
    }, duration);
}

let slideDown = (target, duration=500) => {
    target.style.removeProperty('display');
    let display = window.getComputedStyle(target).display;

    if (display === 'none')
    display = 'block';

    target.style.display = display;
    let height = target.offsetHeight;
    target.style.overflow = 'hidden';
    target.style.height = 0;
    target.style.paddingTop = 0;
    target.style.paddingBottom = 0;
    target.style.marginTop = 0;
    target.style.marginBottom = 0;
    target.offsetHeight;
    target.style.boxSizing = 'border-box';
    target.style.transitionProperty = "height, margin, padding";
    target.style.transitionDuration = duration + 'ms';
    target.style.height = height + 'px';
    target.style.removeProperty('padding-top');
    target.style.removeProperty('padding-bottom');
    target.style.removeProperty('margin-top');
    target.style.removeProperty('margin-bottom');
    window.setTimeout( () => {
        target.style.removeProperty('height');
        target.style.removeProperty('overflow');
        target.style.removeProperty('transition-duration');
        target.style.removeProperty('transition-property');
    }, duration);
}
var slideToggle = (target, duration = 500) => {
    if(window.getComputedStyle(target).display === 'none'){
        return slideDown(target, duration);
    }else{
        return slideUp(target, duration);
    }
}
// [].forEach.call(document.querySelectorAll(".search-btn"), function(el) {
//     el.addEventListener("click", function() {
//         document.getElementById("search-container").classList.toggle("active");
//     });
// });

function app_msg(msg, type='white-bg', dur=2500){
    const msg_popup = document.querySelectorAll('.msg-popup')[0];
    const over = document.querySelectorAll('.overlay')[0];

    msg_popup.querySelector('p').innerHTML = msg;
    msg_popup.classList.remove('danger');
    msg_popup.classList.remove('info');
    msg_popup.classList.remove('success');
    msg_popup.classList.remove('warning');
    msg_popup.classList.add(type);
    over.classList.add('opened');
    msg_popup.classList.add('showw');
    if(dur != 0){
        setTimeout(function(){
            over.classList.remove('opened');
            msg_popup.classList.remove('showw');
            msg_popup.classList.remove('center-popup');
        },dur);
    }else{
        document.querySelector('.close_popup')[0].style.display = 'block';
    }
}
