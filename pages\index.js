import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Faq from "../components/faq"
import Story from "../components/story"
import Link from "next/link"
import Image from "next/image"
import Script from "next/script"
import { getStrapiMedia } from "../lib/media"
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';

const Home = ({ faqs, stories, homepage }) => {
    // Handle case where homepage data is not available
    if (!homepage || !homepage.attributes) {
        return (
            <Layout>
                <main className="homepage">
                    <div className="container">
                        <h1>Loading...</h1>
                        <p>Unable to load homepage content. Please try again later.</p>
                    </div>
                </main>
            </Layout>
        )
    }

    return (
        <Layout>
            <Seo seo={homepage.attributes.seo} />
            <main className="homepage">
                <section className="hero-section">
                    <div className="container-fluid hero-container p-0">
                        <div className="row hero-content m-0">
                            <div className="col-12 hero-item p-0">
                                <div className="bg-holder">
                                    {homepage?.attributes?.hero?.bgimage?.data ? (
                                        <Image
                                            src={getStrapiMedia(homepage.attributes.hero.bgimage)}
                                            alt="ethos homepage"
                                            className="img-fluid"
                                            width={homepage.attributes.hero.bgimage.data.attributes.width}
                                            height={homepage.attributes.hero.bgimage.data.attributes.height}
                                            style={{width: '100%', height: 'auto'}}
                                        />
                                    ) : (
                                        <Image
                                            src="/images/main-img.jpg"
                                            alt="ethos homepage"
                                            className="img-fluid"
                                            width={1920}
                                            height={1080}
                                            style={{width: '100%', height: 'auto'}}
                                        />
                                    )}
                                </div>
                                <div className="txt-holder">
                                    <h1>{homepage?.attributes?.hero?.title || "Welcome to Ethos Tracking"}</h1>
                                    <p>{homepage?.attributes?.hero?.subtitle || "Track your social impact with precision"}</p>
                                    <div className="img-holder">
                                        <div className="bg-holder">
                                            {homepage.attributes.hero.image?.data && (
                                                <Image 
                                                    src={getStrapiMedia(homepage.attributes.hero.image)} 
                                                    alt="Video" 
                                                    className="img-fluid" 
                                                    width={homepage.attributes.hero.image.data.attributes.width} 
                                                    height={homepage.attributes.hero.image.data.attributes.height} 
                                                />
                                            )}
                                        </div>
                                        <div className="play-holder">
                                            <span className="fancybox" data-fancybox="video-gallery" data-src={homepage?.attributes?.hero?.url || "https://www.youtube.com/watch?v=dQw4w9WgXcQ"} data-caption={homepage?.attributes?.hero?.title || "Welcome Video"}>
                                                <span><Image src="/images/play-video-icon.svg" width={112} height={112} className="img-fluid" alt="play button ethos tracking" /></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <div className="bg-texture">
                    <section className="homepage-main-section">
                        <div className="container homepage-main-container">
                            {(Array.isArray(homepage?.attributes?.home_sections?.data) ? homepage.attributes.home_sections.data : []).map((section, i) => {
                                return (
                                    <div className="row aic homepage-main-item" key={'home_sects__' + i}>
                                        <div className={"col-6 txt-holder " + (i % 2 == 0 ? 'order-md-2' : '') + ""}>
                                            <h5>{section?.attributes?.smalltitle || ""}</h5>
                                            <h3>{section?.attributes?.title || "Section Title"}</h3>
                                            <p>{section?.attributes?.description || "Section description"}</p>
                                            <Link href={section?.attributes?.url || "#"} className="btn second-bg white box-shadow" target={(section?.attributes?.external) ? ('_blank') : ('')} title="EXPLORE">{section?.attributes?.button_label || "Learn More"}</Link>
                                        </div>
                                        <div className="col-6 img-holder">
                                            {section?.attributes?.image?.data ? (
                                                <Image
                                                    src={getStrapiMedia(section.attributes.image)}
                                                    alt="Video"
                                                    className="img-fluid"
                                                    width={section.attributes.image.data.attributes.width || 600}
                                                    height={section.attributes.image.data.attributes.height || 400}
                                                />
                                            ) : (
                                                <Image
                                                    src="/images/main-img.jpg"
                                                    alt="Default image"
                                                    className="img-fluid"
                                                    width={600}
                                                    height={400}
                                                />
                                            )}
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                        <div className="pricing-feature-section pt-0">
                            <div className="container pricing-feature-container">
                                <div className="row pricing-feature-content">
                                    <Link href="/pricing" className="col-6 pricing-feature-item">
                                        <div className="icon-holder">
                                            <Image src="/images/dollar-icon.svg" width="1920" height="300" alt="Ethos" className="img-fluid" />
                                        </div>
                                        <div className="txt-holder">
                                            <h3>View Pricing →</h3>
                                            <p>Ethos Tracking offers a better product for a better world–at a better price</p>
                                        </div>
                                    </Link>
                                    <Link href="/contact-us" className="col-12 pricing-feature-item offset-5 col-7">
                                        <div className="icon-holder">
                                            <Image src="/images/question-icon.svg" width="1920" height="300" alt="Ethos" className="img-fluid" />
                                        </div>
                                        <div className="txt-holder">
                                            <h3>Contact Sales →</h3>
                                            <p>Let&apos;s find a solution tailored to your specific needs.</p>
                                        </div>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </section>

                    <section className="homepage-succes-stories-section">
                        <div className="container-fluid">
                            <div className="row heading-content">
                                <div className="col-12 txt-holder text-center">
                                    <h5>SUCCESS STORIES</h5>
                                    <h3>Ethos Tracking Satisfied Customers</h3>
                                </div>
                            </div>
                        </div>
                        <div className="container homepage-succes-stories-container">
                            <Swiper
                                // install Swiper modules
                                modules={[Pagination]}
                                spaceBetween={20}
                                slidesPerView={2}
                                slidesPerGroup={3}
                                navigation
                                pagination={{ clickable: true }}
                                scrollbar={{ draggable: true }}
                                onSwiper={(swiper) => console.log(swiper)}
                                onSlideChange={() => console.log('slide change')}
                                breakpoints={{
                                    320: {
                                        spaceBetween: 10,
                                        slidesPerView: 1,
                                    },
                                    576: {
                                        spaceBetween: 10,
                                        slidesPerView: 1,
                                    },
                                    768: {
                                        spaceBetween: 10,
                                        slidesPerView: 1,
                                    },
                                    1024: {
                                        spaceBetween: 10,
                                        slidesPerView: 1,
                                    },
                                    1280: {
                                        slidesPerView: 2,
                                    },
                                }}
                            >
                                {stories.map((story, i) => {
                                    return (
                                        <SwiperSlide key={`home_swiper_key_${i}`}><Story story={story} idx={i} key={`home_story_key_${i}`} /></SwiperSlide>
                                    )
                                })}
                            </Swiper>
                        </div>
                    </section>

                    <section className="accordion-section">
                        <div className="container mw-900 accordion-container">
                            <div className="row mb-100">
                                <div className="col-12 txt-holder text-center">
                                    <h5>GET ANSWERS</h5>
                                    <h3>Frequently Asked Questions</h3>
                                </div>
                            </div>
                            <div className="row">
                                <div id="accordion" className="col-12">
                                    {faqs.map((faq, i) => {
                                        return (
                                            <Faq faq={faq} key={`home_faq_key_${i}`} />
                                        )
                                    })}
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </Layout>
    )
}

export async function getServerSideProps() {
    try {
        // Run API calls in parallel
        const [faqRes, storiesRes, homepageRes] = await Promise.all([
            fetchAPI("/faqs", {
                populate: "*",
                pagination:{
                    start: 0,
                    limit: 5
                }
            }),
            fetchAPI("/stories", {
                populate: "*",
                // pagination:{
                //     start: 0,
                //     limit: 3
                // }
            }),
            fetchAPI("/homepage", {
                populate: {
                    hero: {
                        populate: "*"
                    },
                    seo: {
                        populate: "*"
                    },
                    home_sections: {
                        populate: "*"
                    }
                }
            }),
        ])

        return {
            props: {
                faqs: faqRes.data || [],
                stories: storiesRes.data || [],
                homepage: homepageRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching homepage data:", error);
        return {
            props: {
                faqs: [],
                stories: [],
                homepage: null,
            }
        }
    }
}

export default Home
