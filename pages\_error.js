import Layout from "../components/layout"
import Seo from "../components/seo"
import Link from "next/link"

const NotFound = () => {
    const seo = {
        metaTitle: "404 page",
        metaDescription: "404 page",
        shareImage: "",
        post: false,
    }

    return (
        <Layout>
            <Seo seo={seo} />
            <main>
                <section className="page-404-section">
                    <div className="container-fluid page-404-container">
                        <div className="row page-404-content">
                            <div className="col-12 page-404-item">
                                <div className="txt-holder">
                                    <h1>404, Page not found</h1>
                                    <p>The page you are looking htmlFor doesn’t exist or an other error occurred.</p>
                                    <Link href="/" className="btn second-bg white">GO BACK HOME</Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </Layout>
    )
}

export default NotFound;