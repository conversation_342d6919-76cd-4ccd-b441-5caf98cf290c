import React from "react"


export default function Forms() {

    const echo_it = () => {
        console.log('RADI');
        const form = $('#contact-us-form');

        console.log(form);
        console.log(form.serializeArray());
    }
    const handleForm = (e) => {
        const form = $('#contact-us-form');
        e.preventDefault();

        const formData = form.serializeArray();
        const formDataObject = {};

        $.map(formData, function(n, i){
            formDataObject[n['name']] = n['value'];
        });
        const formDataNew = {"data": formDataObject};
        $.ajax({
            type: 'POST',
            url: 'http://localhost:1337/api/forms/',
            data: formDataNew,
            dataType: 'json',
            success: function () {
                alert('Success');
                form.trigger('reset');
                location.href = '/thank-you';
            },
            error: function () {
                alert('Error');
            }
        });

        // const emailTemplate = {
        //     subject: 'Welcome <%= user.firstname %>',
        //     text: `Welcome on mywebsite.fr!
        //       Your account is now linked with: <%= user.email %>.`,
        //     html: `<h1>Welcome on mywebsite.fr!</h1>
        //       <p>Your account is now linked with: <%= user.email %>.<p>`,
        // };

        // const sendEmail = await strapi.plugins['email'].services.email.send({
        //     to: '<EMAIL>',
        //     from: '<EMAIL>',
        //     replyTo: '<EMAIL>',
        //     subject: 'Use strapi email provider successfully',
        //     text: 'Hello world!',
        //     html: 'Hello world!',
        // });
    }
    return (
        <div>
            <form id="contact-us-form" onSubmit={handleForm}>
                <div className="input-container">
                    <input type="text" className="" name="name" placeholder="Name" />
                </div>
                <div className="input-container">
                    <input type="email" className="" name="email" placeholder="E-mail Address" />
                </div>
                <div className="input-container">
                    <input type="text" className="" name="phone" placeholder="Phone # (optional)" />
                </div>
                <div className="input-container">
                    <input type="text" className="" name="subject" placeholder="Subject" />
                </div>
                <div className="input-container mb-50">
                    <textarea type="text" rows="6" name="message" placeholder="Message"></textarea>
                </div>
                <div className="input-container">
                    <button type="submit" className="btn second-bg white box-shadow">SUBMIT</button>
                </div>
            </form>
        </div>
    )
}
