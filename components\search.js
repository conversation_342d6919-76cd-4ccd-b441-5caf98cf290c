import { useState } from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import Script from "next/script";

export default function Search({ type }) {
    const [keyword, setKeyword] = useState("");
    const router = useRouter();

    const search_btn = (el) => {
        document.getElementById("search-container").classList.toggle("active");
    }
    const handleSubmit = (e) => {
        e.preventDefault();
        let input = document.querySelector('.search-input').value;
        if(input.length > 2){
            router.push(`/${type}/search?term=${keyword}`);
        }else{
            alert('Enter at least 3 characters');
        }
        setKeyword("");
    };
    return (
        <li className="filter-item" id="search-container">
            <a className="btn border-btn search-btn" id="search-btn" onClick={search_btn}>
                <Image src="/images/search-icon.svg" className="img-fluid" alt="search icon" width="12" height="12" />
            </a>
            <div id="search-form" style={{display: 'none'}}>
                <form onSubmit={handleSubmit}>
                    <input type="text" value={keyword} onChange={(e) => setKeyword(e.target.value)} placeholder="Search posts" className="search-input" />
                    <button className="search-submit-button" type="submit">→</button>
                </form>
            </div>
        </li>
    );
}
