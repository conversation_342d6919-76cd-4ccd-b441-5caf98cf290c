[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18.17.0"
  NPM_FLAGS = "--no-audit --legacy-peer-deps"
  NEXT_PUBLIC_GA_ID = "G-4TXP8N8D63"
  NEXT_PUBLIC_GA_MEASUREMENT_ID = "G-4TXP8N8D63"
  NEXT_PUBLIC_STRAPI_API_URL = "https://phpstack-1462486-5579835.cloudwaysapps.com"

[[plugins]]
  package = "@netlify/plugin-nextjs"

# Redirect rules - handled by Next.js plugin
# [[redirects]]
#   from = "/*"
#   to = "/index.html"
#   status = 200
