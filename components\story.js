import React from "react"
import ReactMarkdown from "react-markdown"
import Image from "next/image"
import { getStrapiMedia } from "../lib/media"

const Story = ({ story, idx }) => {
    const readMore = (e) => {
        console.log(e);
        e.target.parentNode.closest('div').classList.toggle('active');
        e.target.innerHTML = e.target.innerHTML == 'Read More' ? 'Read Less' : 'Read More';
    };

    return (
        <div className="row homepage-succes-stories-item aic">
            {/* <div className={"col-6 img-holder order-md-2" + (idx % 2 != 0 ? 'order-md-2' : '') + ""}>
                <Image src={getStrapiMedia(story.attributes.image)} width={story.attributes.image.data.attributes.width} height={story.attributes.image.data.attributes.height} className="img-fluid" layout="responsive" alt="story image ethos tracking" />
            </div> */}
            <div className="col-12 txt-holder py-5 px-100">
                {story.attributes.NonProfitCustomer ? (
                <span className="swiper-tag">Non-Profit Customer</span>
                ) : (
                <span className="swiper-tag">Business Customer</span>
                )}
                {story.attributes.image && (
                    <div className="icon-holder testimonials-img">
                        <Image src={getStrapiMedia(story.attributes.image)} width={story.attributes.image.data.attributes.width} height={story.attributes.image.data.attributes.height} className="img-fluid" alt="play button story ethos tracking" />
                    </div>
                )}
                <div className="testimonials-desc">
                    <ReactMarkdown
                        source={story.attributes.description}
                        escapeHtml={false}
                    />
                    {story.attributes.description.length > 330 && (
                        <span className="read_more" onClick={readMore}>Read More</span>
                    )}
                </div>
                <p className="name">{story.attributes.fullname}</p>
                <p className="title">{story.attributes.company}</p>
            </div>
        </div>
    )
}

export default Story
