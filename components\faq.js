import React from "react"

const Faq = ({ faq }) => {
    const accordionSlide = (el) => {
        el.target.classList.toggle('active');
        [].forEach.call(document.querySelectorAll(".accordion-toggle"), function(elem) {
            if(elem != el){
                elem.classList.remove('active');
            }
        })
        slideToggle(el.target.nextElementSibling);
        [].forEach.call(document.querySelectorAll(".accordion-content"), function(elemm) {
            if(elemm != el.target.nextElementSibling){
                slideUp(elemm, 200);
            }
        })
    }
    return (
        <div>
            <h4 className="accordion-toggle" onClick={accordionSlide}>{faq.attributes.title}</h4>
            <div className="accordion-content default">
                <div dangerouslySetInnerHTML={{__html: faq.attributes.description}} />
            </div>
        </div>
    )
}

export default Faq
