import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import { fetchAPI } from "../lib/api"
import Image from 'next/image'
import Link from 'next/link'
import Script from "next/script"
import { getStrapiMedia } from "../lib/media"
import { Tab, Tabs, Tab<PERSON>ist, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

const Features = ({ pages }) => {
    const firstSecondSection = (pages.attributes.sections).slice(0, 2)
    const otherSections = (pages.attributes.sections).slice(2, (pages.attributes.sections).length)

    const changeTab = (e) => {
        console.log(e);
        var leversTabHide = document.getElementsByClassName("model-tabs");
        for(var i = 0; i < leversTabHide.length; i++){ leversTabHide[i].classList.remove('react-tabs__tab--selected'); }
        e.target.classList.add('react-tabs__tab--selected');

        var leversHide = document.getElementsByClassName("all_levers");
        for(var i = 0; i < leversHide.length; i++){ leversHide[i].style.display = "none"; }

        let tab = e.target.dataset.tab;
        var leversShow = document.getElementsByClassName(tab);
        for(var i = 0; i < leversShow.length; i++){ leversShow[i].style.display = "block"; }
        console.log(tab);
    }

  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main className="features-page">
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src="/images/main-img.jpg" width="1920" height="300" alt="Ethos" className="img-fluid" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="features-subheader-section">
                <div className="container features-offer-container">
                    <div className="row heading-holder">
                        <div className="col-12 txt-holder text-center">
                            <h1>Supercharge Impact Tracking, Management and Reporting</h1>
                            <p className="f-24">A holistic way to measure your impact.</p>
                            <Link href="/contact-us" className="btn second-bg white box-shadow">GET STARTED</Link>
                        </div>
                    </div>
                </div>
            </section>

            <section className="features-main-section features-main-section-new">
                <div className="container features-main-container">
                    <div className="row features-main-content">
                    {firstSecondSection.map((sect, i) => {
                        return (
                        <div className="col-6 features-video-holder" key={'first_sects__' + i}>
                            {/* <h5>{sect.smalltitle}</h5> */}
                            <h3>{sect.title}</h3>
                            {/* <p>{sect.description}</p>
                            <Link href={sect.url}><a className="btn second-bg white box-shadow">GET STARTED</a></Link> */}
                            <div className="img-holder">
                                {sect.image?.data && (
                                    <div style={{display: "inline-block", maxWidth: "100%", overflow: "hidden", position: "relative", boxSizing: "border-box", margin: 0}}>
                                        <div style={{boxSizing: "border-box", display: "block", maxWidth: "100%"}}>
                                            <img 
                                                alt="" 
                                                aria-hidden="true" 
                                                src={`data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iJHtzZWN0LmltYWdlLmRhdGEuYXR0cmlidXRlcy53aWR0aH0iIGhlaWdodD0iJHtzZWN0LmltYWdlLmRhdGEuYXR0cmlidXRlcy5oZWlnaHR9IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIvPg==`} 
                                                style={{maxWidth: "100%", display: "block", margin: 0, border: "none", padding: 0}} 
                                            />
                                        </div>
                                        <Image 
                                            src={getStrapiMedia(sect.image)} 
                                            width={sect.image.data.attributes.width || 1200} 
                                            height={sect.image.data.attributes.height || 800} 
                                            alt="Ethos" 
                                            className="img-fluid" 
                                            style={{
                                                position: "absolute", 
                                                inset: 0, 
                                                boxSizing: "border-box", 
                                                padding: 0, 
                                                border: "none", 
                                                margin: "auto", 
                                                display: "block", 
                                                width: 0, 
                                                height: 0, 
                                                minWidth: "100%", 
                                                maxWidth: "100%", 
                                                minHeight: "100%", 
                                                maxHeight: "100%"
                                            }}
                                        />
                                    </div>
                                )}
                                <div className="play-holder">
                                    <a data-fancybox="" data-src={sect.youtube} data-caption={sect.title}>
                                        <span>
                                        <Image src="/images/play-video-icon.svg" width="112" height="112" alt="Ethos" className="img-fluid" />
                                        </span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        )
                    })}
                    </div>
                </div>
            </section>
            <section className="values-section platform-section">
                <div className="container values-container">
                    <div className="row values-content">
                        <div className="col-6 txt-holder order-md-2 pt-2">
                            <h5>PLATFORM</h5>
                            <h3>Is Ethos Tracking Right for You?</h3>
                            <div className="custom-tabs">
                            <Tabs defaultIndex={1} onSelect={(index) => {document.getElementById('tabs_image').setAttribute('srcset', '/_next/image?url=%2Fimages%2Ftab-' + index + '.png&w=750&q=75 1x, /_next/image?url=%2Fimages%2Ftab-' + index + '.png&w=1920&q=75 2x'), document.getElementById('tabs_image').setAttribute('src', '/images/tab-' + index + '.png')}}>
                                <TabList>
                                    <Tab>BUSINESSES</Tab>
                                    <Tab>NON-PROFITS</Tab>
                                    <Tab>FOUNDATIONS</Tab>
                                    <Tab>FAMILIES</Tab>
                                </TabList>
                                <TabPanel>
                                    <ul>
                                        <li>You want your social impact work to matter </li>
                                        <li>Your social impact efforts extend beyond cutting checks </li>
                                        <li>You are seeking to track multiple social impact efforts and programs </li>
                                        <li>You value KPIs related to impact work </li>
                                        <li>You have someone in your organization tasked with leading and managing this work</li>
                                        <li>You share or want to share your good work internally and with the world</li>
                                        <li>You recognize that social impact work is as critical to the success of your company as any other strategic initiative.</li>
                                        <li>You wish the work of doing good would be easier to manage and organize</li>
                                    </ul>
                                </TabPanel>
                                <TabPanel>
                                    <ul>
                                        <li>You want your work to be as impactful as possible  </li>
                                        <li>You are seeking to track multiple efforts, programs, and donor relationships</li>
                                        <li>You value KPIs related to your work </li>
                                        <li>You have someone in your organization tasked with managing data</li>
                                        <li>You share or want to share your good work internally and with the world</li>
                                        <li>You wish it was easier to manage and organize your data </li>
                                        <li>Your donors and key partners demand best in class tracking, data, and analysis</li>
                                    </ul>
                                </TabPanel>
                                <TabPanel>
                                    <ul>
                                        <li>You want your work to be as impactful as possible  </li>
                                        <li>You are seeking to track multiple efforts, programs, and relationships</li>
                                        <li>You value KPIs related to your work </li>
                                        <li>You have someone in your organization tasked with managing data</li>
                                        <li>You share or want to share your good work internally and with the world</li>
                                        <li>You wish it was easier to manage and organize your data </li>
                                        <li>You desire best in class tracking, data, and analysis </li>
                                    </ul>
                                </TabPanel>
                                <TabPanel>
                                    <ul>
                                        <li>You want your work to be as impactful as possible  </li>
                                        <li>You are seeking to track multiple efforts, programs, and relationships</li>
                                        <li>You value KPIs related to your work </li>
                                        <li>You have someone in your organization tasked with managing data</li>
                                        <li>You share or want to share your good work internally and with the world</li>
                                        <li>You wish it was easier to manage and organize your data </li>
                                        <li>You desire best in class tracking, data, and analysis</li>
                                    </ul>
                                </TabPanel>
                            </Tabs>
                            {/* <div className="tabss">
                                <span className="tab-link active" data-tab="business-tab" onClick={changeTab('business-tab')}>BUSINESSES</span>
                                <span className="tab-link" data-tab="nonprofit-tab" onClick={changeTab(this)}>NON-PROFITS</span>
                                <span className="tab-link" data-tab="foundation-tab" onClick={changeTab(this)}>FOUNDATIONS</span>
                                <span className="tab-link" data-tab="families-tab" onClick={changeTab(this)}>FAMILIES</span>
                            </div>
                            <div className="tab-content active" id="business-tab">
                            </div>
                            <div className="tab-content" id="nonprofit-tab">
                            </div>
                            <div className="tab-content" id="foundation-tab">
                                <ul>
                                </ul>
                            </div>
                            <div className="tab-content" id="families-tab">
                                <ul>
                                </ul>
                            </div> */}
                            </div>
                        </div>
                        <div className="col-6 img-container">
                            <div className="img-holder">
                                <Image src="/images/ethos-right-for-you.png" alt="Video" width={670} height={730} className="img-fluid" id="tabs_image" />
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <div className="bg-texture">
                <section className="pricing-feature-section">
                    <div className="container pricing-feature-container">
                        <div className="row pricing-feature-content">
                            <Link href="/pricing">
                                <a className="col-6 pricing-feature-item">
                                    <div className="icon-holder">
                                        <Image src="/images/dollar-icon.svg" width="1920" height="300" alt="Ethos" className="img-fluid" />
                                    </div>
                                    <div className="txt-holder">
                                        <h3>View Pricing→</h3>
                                        <p>Ethos Tracking offers a better product for a better world–at a better price</p>
                                    </div>
                                </a>
                            </Link>
                            <Link href="/contact-us">
                                <a className="col-12 pricing-feature-item offset-5 col-7">
                                    <div className="icon-holder">
                                        <Image src="/images/question-icon.svg" width="1920" height="300" alt="Ethos" className="img-fluid" />
                                    </div>
                                    <div className="txt-holder">
                                        <h3>Contact Sales→</h3>
                                        <p>Let&apos;s find a solution tailored to your specific needs.</p>
                                    </div>
                                </a>
                            </Link>
                        </div>
                    </div>
                </section>

                {/* <section className="features-offer-section">
                    <div className="container features-offer-container">
                        {otherSections.map((sect, i) => {
                            if(i == 0 || i == 1){
                                return(
                                    <div className="row features-offer-item aic" key={'other_sects3__' + i}>
                                        <div className={"col-6 txt-holder order-mob-2 " + (i == 0 ? 'order-md-2' : '') + ""}>
                                            <h5>{sect.smalltitle}</h5>
                                            <h3>{sect.title}</h3>
                                            <p>{sect.description}</p>
                                            {sect.url && (
                                                <Link href={sect.url} className="btn second-bg white box-shadow">GET STARTED</Link>
                                            )}
                                        </div>
                                        <div className="col-6 img-holder">
                                            <Image src={getStrapiMedia(sect.image)} width={sect.image.data.attributes.width} height={sect.image.data.attributes.height} alt="Ethos" className="img-fluid" />
                                        </div>
                                    </div>
                                )
                            }
                        })}
                    </div>
                </section> */}

                {/* <section className="feature-section">
                    <div className="container feature-container">
                    {otherSections.map((sect, i) => {
                        if(i == 2){
                            return (
                                <div className="row aic feature-content" key={'other_sects2__' + i}>
                                    <div className="col-6 txt-holder">
                                        <h5>{sect.smalltitle}</h5>
                                        <h3>{sect.title}</h3>
                                        <p>{sect.description}</p>
                                        {(sect.url) && (
                                            <Link href={sect.url} className="btn second-bg white box-shadow">{sect.button_label ? sect.button_label : 'EXPLORE'}</Link>
                                        )}
                                    </div>
                                    <div className="col-6 img-holder">
                                        <Image src={getStrapiMedia(sect.image)} width={sect.image.data.attributes.width} height={sect.image.data.attributes.height} alt="Ethos" className="img-fluid" />
                                    </div>
                                </div>
                            )
                        }
                    })}
                    </div>
                </section> */}
                <section className="lever-section pt-0">
                    <div className="container lever-container">
                        <div className="row heading-holder">
                        {otherSections.map((sect, i) => {
                            if(i == 2){
                                return (
                                    <div className="col-12 text-center txt-holder" key={'other_sects__' + i}>
                                        <h5>{sect.smalltitle}</h5>
                                        <h3>{sect.title}</h3>
                                        <p className="max640">{sect.description}</p>
                                    </div>
                                )
                            }
                        })}
                        </div>
                        <div className="row lever-content">
                            <div className="col-12 custom-tabs text-center">
                                <ul className="react-tabs__tab-list jcc" role="tablist">
                                    <li onClick={changeTab} data-tab="all_levers" className="model-tabs react-tabs__tab react-tabs__tab--selected">ALL</li>
                                    <li onClick={changeTab} data-tab="Give" className="model-tabs react-tabs__tab">GIVE</li>
                                    <li onClick={changeTab} data-tab="Do" className="model-tabs react-tabs__tab">DO</li>
                                    <li onClick={changeTab} data-tab="Lead" className="model-tabs react-tabs__tab">LEAD</li>
                                </ul>
                            </div>
                            {(pages.attributes.levers).map((lever, i) => {
                                return (
                                    <div className={"col-4 all_levers " + lever.LeverModel} key={'other_levers__' + i}>
                                        <div className="lever-item">
                                            <div className="icon-holder">
                                                {
                                                    (lever.image && lever.image != null && lever.image != '') ?
                                                    (<Image src={getStrapiMedia(lever.image)} alt="Cash" width={lever.image.data.attributes.width} height={lever.image.data.attributes.width} className="img-fluid" />)
                                                    :
                                                    (<p>NO IMAGE</p>)
                                                }
                                            </div>
                                            <h3>{lever.title}</h3>
                                            <p>{lever.description}</p>
                                        </div>
                                    </div>
                                )
                            })}
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </Layout>
  )
}


export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/10", {
            populate: {
                sections: {
                    populate: {
                        image: "*"
                    }
                },
                levers: {
                    populate: {
                        image: "*"
                    }
                },
                seo: {
                    populate: "*"
                }
            }
        })

        return {
            props: {
                pages: pagesRes.data || null,
            }
        }
    } catch (error) {
        console.error("Error fetching features page data:", error);
        return {
            props: {
                pages: null,
            }
        }
    }
}

export default Features
