import Document, { Html, <PERSON>, Main, NextScript } from "next/document"

class MyDocument extends Document {
    render() {
        return (
            <Html>
                <Head>
                    {/* eslint-disable-next-line */}
                    {/* <link rel="manifest" href="/manifest.json" /> */}
                    <link href="/favicon-16x16.png" rel="icon" type="image/png" sizes="16x16" />
                    <link href="/favicon-32x32.png" rel="icon" type="image/png" sizes="32x32" />
                    <link rel="apple-touch-icon" href="/icon.png"></link>
                    <meta name="theme-color" content="#fff" />
                    <meta name="robots" content="index,follow" />
                    <meta name="googlebot" content="index,follow" />
                    <script async type="text/javascript" src="/js/custom.js"></script>
                    <script
                      dangerouslySetInnerHTML={{
                        __html: `
                            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                            })(window,document,'script','dataLayer','GTM-TZLNLRBN');
                        `,
                      }}
                    />
                </Head>
                <body>
                    <noscript
                      dangerouslySetInnerHTML={{
                        __html: `
                          <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-TZLNLRBN" height="0" width="0" style="display:none;visibility:hidden"></iframe>
                        `,
                      }}
                    />
                    <Main />
                    <NextScript />
                </body>
            </Html>
        )
    }
}

export default MyDocument
