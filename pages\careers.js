import React from "react"
import Layout from "../components/layout"
import Seo from "../components/seo"
import Position from "../components/position"
import { fetchAPI } from "../lib/api"
import Image from "next/image"
import Link from 'next/link';
import { getStrapiMedia } from "../lib/media"

const Careers = ({ pages, positions }) => {
  const scrollPositions = (elementY, duration) => {
    var startingY = window.pageYOffset;
    var diff = elementY - startingY;
    var start;

    // Bootstrap our animation - it will get called right before next frame shall be rendered.
    window.requestAnimationFrame(function step(timestamp) {
      if (!start) start = timestamp;
      // Elapsed milliseconds since start of scrolling.
      var time = timestamp - start;
      // Get percent of completion in range [0, 1].
      var percent = Math.min(time / duration, 1);

      window.scrollTo(0, startingY + diff * percent);

      // Proceed with animation as long as we wanted it to.
      if (time < duration) {
        window.requestAnimationFrame(step);
      }
    })
  }
  return (
    <Layout pages={pages}>
        <Seo seo={pages.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(pages.attributes.headerimage)} width="1920" height="300" alt="Ethos" className="img-fluid" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{pages.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="main-careers-section">
                <div className="container main-careers-container">
                    {(pages.attributes.sections).map((section, i) => {
                        return (
                            <div className="row main-careers-content aic" key={'sectss__' + i}>
                                <div className="col-6 txt-holder">
                                    <h5>{section.smalltitle}</h5>
                                    <h3>{section.title}</h3>
                                    <p>{section.description}</p>
                                    <a href="#open-positions" className="btn second-bg white box-shadow">VIEW POSITIONS  ↓</a>
                                </div>
                                <div className="col-6 img-holder">
                                    <div className="icon-holder">
                                        <Image src="/images/circle-icon.svg" alt="Careers" width="60" height="60" className="img-fluid" />
                                    </div>
                                    <Image src={getStrapiMedia(section.image)} alt="Careers" width={section.image.data.attributes.width} height={section.image.data.attributes.height} className="img-fluid w-100" />
                                </div>
                            </div>
                        )
                    })}
                </div>
            </section>

            <section className="img-section">
                <div className="container img-container">
                    <div className="row img-content">
                        <div className="col-4 mt-50">
                            <Image src="/images/careers-01.jpg" width="440" height="600" alt="Careers" className="img-fluid" />
                        </div>
                        <div className="col-4 mt-100">
                            <Image src="/images/careers-02.jpg" width="440" height="600" alt="Careers" className="img-fluid" />
                        </div>
                        <div className="col-4">
                            <Image src="/images/careers-03.jpg" width="440" height="600" alt="Careers" className="img-fluid" />
                        </div>
                    </div>
                </div>
            </section>

            <section id="open-positions" className="open-positions-section pt-0" style={{background:"url(images/bg-bottom-right.svg) no-repeat bottom right /cover"}}>
                <div className="container open-positions-container mw-1240">
                    <div className="row open-positions-heading">
                        <div className="col-12 txt-holder text-center">
                            <h5>OPEN POSITIONS</h5>
                            <h3 className="mb-5">Ethos Tracking Career Opportunities</h3>
                        </div>
                    </div>
                    <div className="row">
                        {positions.map((position, i) => {
                            return (
                                <Position position={position} key={`career_position_key_${i}`} />
                            )
                        })}
                    </div>
                </div>
            </section>
        </main>
    </Layout>
  )
}

export async function getServerSideProps() {
    try {
        const pagesRes = await fetchAPI("/pages/7", {
            populate: {
                sections: {
                    populate: {
                        image: "*"
                    }
                },
                image: {
                    populate: "*"
                },
                headerimage: {
                    populate: "*"
                },
                seo: {
                    populate: "*"
                }
            }
        })
        const positionsRes = await fetchAPI("/careers", {populate: "*"})
        return {
            props: {
                pages: pagesRes.data || null,
                positions: positionsRes.data || [],
            }
        }
    } catch (error) {
        console.error("Error fetching careers data:", error);
        return {
            props: {
                pages: null,
                positions: [],
            }
        }
    }
}

export default Careers