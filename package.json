{"name": "ethos-tracking", "version": "0.1.5", "private": true, "scripts": {"develop": "next dev", "dev": "next dev", "build": "next build", "build:netlify": "node netlify-build.js", "start": "next start", "deploy": "next build", "lint": "next lint", "lint:fix": "next lint --fix", "build:local": "next build", "test:build": "next build && next start"}, "dependencies": {"@fancyapps/ui": "^4.0.10", "bootstrap": "^5.3.7", "moment": "^2.29.4", "next": "^13.5.6", "next-absolute-url": "^1.2.2", "next-pwa": "^5.6.0", "next-seo": "^6.4.0", "next-share": "^0.20.0", "nextjs-google-analytics": "^2.3.3", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hubspot-form": "^1.3.7", "react-markdown": "^8.0.7", "react-moment": "^1.1.3", "react-tabs": "^6.0.2", "sharp": "^0.32.6", "swiper": "^8.4.7", "tslib": "^2.6.2"}, "license": "MIT", "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^13.5.6", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.8"}}