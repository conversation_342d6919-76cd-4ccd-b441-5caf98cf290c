import React from "react"
import Learncard from "../components/learncard"
import Categories from "../components/categories"
import Layout from "../components/layout"
import Seo from "../components/seo"
import Search from "../components/search"
import Link from "next/link"
import Image from "next/image"
import Script from "next/script"
import { fetchAPI } from "../lib/api"
import { getStrapiMedia } from "../lib/media"

const Learn = ({ posts, categories, learnpage }) => {
  return (
    <Layout>
        <Seo seo={learnpage.attributes.seo} />
        <main>
            <section className="main-img-section">
                <div className="container-fluid main-img-container">
                    <div className="row main-img-content">
                        <div className="col-12 main-img-item">
                            <div className="bg-holder">
                                <Image src={getStrapiMedia(learnpage.attributes.headerimage)} width={learnpage.attributes.headerimage.data.attributes.width} height={learnpage.attributes.headerimage.data.attributes.height} className="img-fluid" alt="blog header image ethos tracking" />
                            </div>
                            <div className="heading-holder">
                                <div className="container">
                                    <h1>{learnpage.attributes.title}</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section className="main-blog-section">
                <div className="container filter-container mb-100">
                    <div className="row filter-content">
                        <div className="col-6 filter-heading">
                            <h2>Watch. Get Inspired.</h2>
                        </div>
                        <ul className="col-6 filter-items learn_categories">
                            <li className="filter-item">
                                <Link href={"/videos"} className="active btn white second-bg box-shadow">ALL</Link>
                            </li>
                            {categories.map((category, i) => {
                                return (
                                    <Categories
                                        category={category}
                                        type={'videos'}
                                        key={`learn_single_category__${i}`}
                                    />
                                )
                            })}
                            <Search type={'learn'} />
                        </ul>
                    </div>
                </div>
                <div className="container blog-group-container mb-150">
                    <div className="row blog-group-content">
                        {posts.map((post, i) => {
                            return (
                                <Learncard
                                    post={post}
                                    key={`learn_posts__${i}`}
                                />
                            )
                        })}
                    </div>
                </div>
            </section>
        </main>
        <Script async src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js" />
    </Layout>
  )
}

export async function getServerSideProps() {
  try {
    // Run API calls in parallel
    const [postsRes, categoriesRes, learnRes] = await Promise.all([
      fetchAPI("/learns", {
          populate: "*",
          sort: 'createdAt:desc'
      }),
      fetchAPI("/categories", { populate: "*" }),
      fetchAPI("/pages/6", { populate: "*" }),
    ])
    return {
      props: {
        posts: postsRes.data || [],
        categories: categoriesRes.data || [],
        learnpage: learnRes.data || null
      }
    }
  } catch (error) {
    console.error("Error fetching videos data:", error);
    return {
      props: {
        posts: [],
        categories: [],
        learnpage: null
      }
    }
  }
}

export default Learn
