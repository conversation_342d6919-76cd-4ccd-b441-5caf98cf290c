import App from "next/app"
import Head from "next/head"
import "bootstrap/dist/css/bootstrap.min.css"
import "../assets/css/style.css"
import { createContext } from "react"
import { fetchAPI } from "../lib/api"
import { getStrapiMedia } from "../lib/media"
import Script from 'next/script'

// Store Strapi Global object in context
export const GlobalContext = createContext({})

const MyApp = ({ Component, pageProps }) => {
  const { global } = pageProps

  return (
    <>
      <Head>
        <link
          rel="shortcut icon"
          href={global?.attributes?.favicon?.data?.attributes?.url || "/favicon.ico"}
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link
          rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css"
        />
      </Head>
      <GlobalContext.Provider value={global?.attributes || {}}>
        <Component {...pageProps} />
      </GlobalContext.Provider>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}`}
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){window.dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID}');
        `}
      </Script>
      <Script
        src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"
        strategy="afterInteractive"
      />
      <Script id="fancybox-init" strategy="afterInteractive">
        {`
          document.addEventListener('DOMContentLoaded', function() {
            if (typeof Fancybox !== 'undefined') {
              Fancybox.bind('[data-fancybox]', {
                // Options
                Toolbar: {
                  display: {
                    left: [],
                    middle: [],
                    right: ["close"]
                  }
                },
                Thumbs: false,
                animated: true,
                hideScrollbar: false,
                parentEl: document.body,
                dragToClose: false,
                keyboard: {
                  Escape: "close"
                },
                wheel: false,
                touch: {
                  vertical: false,
                  momentum: false
                }
              });
            }
          });
        `}
      </Script>
    </>
  )
}

// getInitialProps disables automatic static optimization for pages that don't
// have getStaticProps. So article, category and home pages still get SSG.
// Hopefully we can replace this with getStaticProps once this issue is fixed:
// https://github.com/vercel/next.js/discussions/10949
MyApp.getInitialProps = async (ctx) => {
  // Calls page's `getInitialProps` and fills `appProps.pageProps`
  const appProps = await App.getInitialProps(ctx)
  // Fetch global site settings from Strapi
  try {
    const globalRes = await fetchAPI("/global", {
      populate: {
        favicon: "*",
        defaultSeo: {
          populate: "*",
        },
        social: "*",
      },
    })
    // Pass the data to our page via props
    return { ...appProps, pageProps: { ...appProps.pageProps, global: globalRes.data } }
  } catch (error) {
    console.error("Error fetching global data:", error)
    return { ...appProps }
  }
}

export default MyApp
